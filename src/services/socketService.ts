import { io, Socket } from 'socket.io-client';
import { Participant } from '@/pages/Index';

export interface GameState {
  participants: Participant[];
  currentPhase: 'registration' | 'waiting' | 'arena' | 'results';
  currentChallenge?: number;
  timeLeft?: number;
  isActive?: boolean;
  roomId: string;
}

export interface SocketEvents {
  // Client to Server
  'join-room': (data: { roomId: string; playerName: string }) => void;
  'start-game': (roomId: string) => void;
  'update-score': (data: { roomId: string; participantId: string; points: number }) => void;
  'next-challenge': (roomId: string) => void;
  'end-challenge': (roomId: string) => void;
  'reset-game': (roomId: string) => void;

  // Server to Client
  'game-state-updated': (gameState: GameState) => void;
  'participant-joined': (participant: Participant) => void;
  'participant-left': (participantId: string) => void;
  'challenge-started': (challengeIndex: number) => void;
  'challenge-ended': () => void;
  'score-updated': (data: { participantId: string; newScore: number }) => void;
  'error': (message: string) => void;
}

class SocketService {
  private socket: Socket | null = null;
  private roomId: string | null = null;
  private isConnected: boolean = false;

  // En un entorno real, esto sería la URL del servidor
  private readonly SERVER_URL = 'ws://localhost:3001';

  connect(): Promise<Socket> {
    return new Promise((resolve, reject) => {
      if (this.socket?.connected) {
        resolve(this.socket);
        return;
      }

      // Para desarrollo local, simularemos el servidor con localStorage
      this.socket = this.createMockSocket();
      this.isConnected = true;
      resolve(this.socket);
    });
  }

  private createMockSocket(): Socket {
    // Simulación de socket para desarrollo local
    const mockSocket = {
      connected: true,
      id: Math.random().toString(36).substr(2, 9),
      
      emit: (event: string, data?: any) => {
        console.log('Socket emit:', event, data);
        this.handleMockServerResponse(event, data);
      },

      on: (event: string, callback: Function) => {
        console.log('Socket listening to:', event);
        // Almacenar callbacks para eventos
        if (!this.eventCallbacks) {
          this.eventCallbacks = new Map();
        }
        this.eventCallbacks.set(event, callback);
      },

      off: (event: string) => {
        if (this.eventCallbacks) {
          this.eventCallbacks.delete(event);
        }
      },

      disconnect: () => {
        this.isConnected = false;
        console.log('Socket disconnected');
      }
    } as any;

    return mockSocket;
  }

  private eventCallbacks: Map<string, Function> = new Map();

  private handleMockServerResponse(event: string, data: any) {
    // Simulación de respuestas del servidor
    setTimeout(() => {
      switch (event) {
        case 'join-room':
          this.handleJoinRoom(data);
          break;
        case 'start-game':
          this.handleStartGame(data);
          break;
        case 'update-score':
          this.handleUpdateScore(data);
          break;
        case 'next-challenge':
          this.handleNextChallenge(data);
          break;
        case 'end-challenge':
          this.handleEndChallenge(data);
          break;
        case 'reset-game':
          this.handleResetGame(data);
          break;
      }
    }, 100); // Simular latencia de red
  }

  private handleJoinRoom(data: { roomId: string; playerName: string }) {
    const gameState = this.getGameState(data.roomId);
    const newParticipant: Participant = {
      id: Math.random().toString(36).substr(2, 9),
      name: data.playerName,
      score: 0,
      isHost: gameState.participants.length === 0
    };

    gameState.participants.push(newParticipant);
    this.saveGameState(data.roomId, gameState);

    // Notificar a todos los participantes
    this.emitToRoom('participant-joined', newParticipant);
    this.emitToRoom('game-state-updated', gameState);
  }

  private handleStartGame(roomId: string) {
    const gameState = this.getGameState(roomId);
    gameState.currentPhase = 'arena';
    gameState.currentChallenge = 0;
    gameState.isActive = true;
    this.saveGameState(roomId, gameState);

    this.emitToRoom('challenge-started', 0);
    this.emitToRoom('game-state-updated', gameState);
  }

  private handleUpdateScore(data: { roomId: string; participantId: string; points: number }) {
    const gameState = this.getGameState(data.roomId);
    const participant = gameState.participants.find(p => p.id === data.participantId);
    
    if (participant) {
      participant.score += data.points;
      this.saveGameState(data.roomId, gameState);
      
      this.emitToRoom('score-updated', { 
        participantId: data.participantId, 
        newScore: participant.score 
      });
      this.emitToRoom('game-state-updated', gameState);
    }
  }

  private handleNextChallenge(roomId: string) {
    const gameState = this.getGameState(roomId);
    if (gameState.currentChallenge !== undefined) {
      gameState.currentChallenge++;
      if (gameState.currentChallenge >= 4) {
        gameState.currentPhase = 'results';
        gameState.isActive = false;
      }
    }
    this.saveGameState(roomId, gameState);
    this.emitToRoom('game-state-updated', gameState);
  }

  private handleEndChallenge(roomId: string) {
    const gameState = this.getGameState(roomId);
    gameState.isActive = false;
    this.saveGameState(roomId, gameState);
    
    this.emitToRoom('challenge-ended');
    this.emitToRoom('game-state-updated', gameState);
  }

  private handleResetGame(roomId: string) {
    const gameState = this.getGameState(roomId);
    gameState.currentPhase = 'waiting';
    gameState.currentChallenge = 0;
    gameState.isActive = false;
    gameState.participants.forEach(p => p.score = 0);
    this.saveGameState(roomId, gameState);
    
    this.emitToRoom('game-state-updated', gameState);
  }

  private getGameState(roomId: string): GameState {
    const stored = localStorage.getItem(`gameState_${roomId}`);
    if (stored) {
      return JSON.parse(stored);
    }
    
    return {
      participants: [],
      currentPhase: 'registration',
      roomId
    };
  }

  private saveGameState(roomId: string, gameState: GameState) {
    localStorage.setItem(`gameState_${roomId}`, JSON.stringify(gameState));
  }

  private emitToRoom(event: string, data?: any) {
    const callback = this.eventCallbacks.get(event);
    if (callback) {
      callback(data);
    }
  }

  joinRoom(roomId: string, playerName: string) {
    this.roomId = roomId;
    if (this.socket) {
      this.socket.emit('join-room', { roomId, playerName });
    }
  }

  startGame() {
    if (this.socket && this.roomId) {
      this.socket.emit('start-game', this.roomId);
    }
  }

  updateScore(participantId: string, points: number) {
    if (this.socket && this.roomId) {
      this.socket.emit('update-score', { 
        roomId: this.roomId, 
        participantId, 
        points 
      });
    }
  }

  nextChallenge() {
    if (this.socket && this.roomId) {
      this.socket.emit('next-challenge', this.roomId);
    }
  }

  endChallenge() {
    if (this.socket && this.roomId) {
      this.socket.emit('end-challenge', this.roomId);
    }
  }

  resetGame() {
    if (this.socket && this.roomId) {
      this.socket.emit('reset-game', this.roomId);
    }
  }

  onGameStateUpdated(callback: (gameState: GameState) => void) {
    if (this.socket) {
      this.socket.on('game-state-updated', callback);
    }
  }

  onParticipantJoined(callback: (participant: Participant) => void) {
    if (this.socket) {
      this.socket.on('participant-joined', callback);
    }
  }

  onScoreUpdated(callback: (data: { participantId: string; newScore: number }) => void) {
    if (this.socket) {
      this.socket.on('score-updated', callback);
    }
  }

  onChallengeStarted(callback: (challengeIndex: number) => void) {
    if (this.socket) {
      this.socket.on('challenge-started', callback);
    }
  }

  onChallengeEnded(callback: () => void) {
    if (this.socket) {
      this.socket.on('challenge-ended', callback);
    }
  }

  onError(callback: (message: string) => void) {
    if (this.socket) {
      this.socket.on('error', callback);
    }
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      this.roomId = null;
    }
  }

  isSocketConnected(): boolean {
    return this.isConnected && this.socket?.connected;
  }
}

export const socketService = new SocketService();

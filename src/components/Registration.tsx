
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface RegistrationProps {
  onRegister: (name: string, multiplayer?: boolean) => void;
}

const Registration = ({ onRegister }: RegistrationProps) => {
  const [name, setName] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (name.trim()) {
      onRegister(name.trim(), true); // Always multiplayer
    }
  };

  return (
    <div className="flex justify-center items-center min-h-[60vh]">
      <Card className="w-full max-w-md bg-game-card border-2 border-cyan-500/40 backdrop-blur-sm shadow-2xl">
        <CardHeader className="text-center">
          <CardTitle className="text-4xl font-arcade text-neon-accent mb-4 animate-neon-pulse">
            🌍 BATALLA GLOBAL
          </CardTitle>
          <p className="text-high-contrast font-retro text-lg mb-2">
            Únete a la arena global épica
          </p>
          <p className="text-neon-primary font-retro text-sm mb-4">
            ⚔️ Todos los guerreros luchan en la misma batalla
          </p>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <Input
                type="text"
                placeholder="Tu nombre de batalla épico..."
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="h-16 text-xl input-game font-bold"
                maxLength={20}
              />
            </div>

            <Button
              type="submit"
              className="w-full h-16 text-xl btn-game-primary hover:scale-105 transition-all duration-300 animate-glow font-arcade"
              disabled={!name.trim()}
            >
              <div className="text-center">
                <div className="text-2xl">🌍 ENTRAR A LA ARENA GLOBAL</div>
                <div className="text-sm opacity-90 font-retro">Batalla Multijugador</div>
              </div>
            </Button>
          </form>
          
          <div className="mt-8 p-6 bg-game-card-alt rounded-lg border-3 border-purple-500/40">
            <h3 className="text-xl font-arcade text-neon-accent mb-4 animate-text-glow">
              🏆 SOBRE LA BATALLA:
            </h3>
            <div className="text-readable space-y-3">
              <p className="text-lg">• 🧠 Desafíos únicos que pondrán a prueba tu dominio de IA</p>
              <p className="text-lg">• ⏱️ 3 minutos por ronda de máxima intensidad</p>
              <p className="text-lg">• 🎯 Puntos por velocidad y precisión</p>
              <p className="text-lg">• 🚀 ¡Aprende mientras compites contra otros desarrolladores!</p>
              <p className="text-lg">• 🏅 Demuestra que eres el maestro de los prompts</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Registration;

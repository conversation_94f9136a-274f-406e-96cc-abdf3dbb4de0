import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Participant } from '@/pages/Index';
import { GoogleGenerativeAI } from '@google/generative-ai';

interface PromptBattleProps {
  timeLeft: number;
  participants: Participant[];
  onUpdateScore: (participantId: string, points: number) => void;
}

const PromptBattle = ({ timeLeft, participants, onUpdateScore }: PromptBattleProps) => {
  const [userPrompt, setUserPrompt] = useState('');
  const [submitted, setSubmitted] = useState(false);
  const [aiResponse, setAiResponse] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [score, setScore] = useState(0);

  const challengePrompt = `Quiero que obtengas los personajes principales de Rick and Morty de esta API: https://rickandmortyapi.com/api/character

Por favor, ayúdame a crear un prompt que me permita obtener:

1. Los nombres de los 5 personajes principales
2. Su especie y origen  
3. Una breve descripción de cada uno
4. Su estado (vivo, muerto, desconocido)
5. En qué episodios aparecen más frecuentemente

Escribe un prompt claro y directo que pueda usar con cualquier IA para obtener esta información de manera organizada y fácil de entender.`;

  const handleSubmit = async () => {
    if (userPrompt.trim() && !submitted) {
      setSubmitted(true);
      setIsLoading(true);
      
      try {
        // Initialize Gemini API
        const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
        if (!apiKey) {
          throw new Error('API key not found');
        }
        
        const genAI = new GoogleGenerativeAI(apiKey);
        const model = genAI.getGenerativeModel({ model: "gemini-pro" });

        const result = await model.generateContent(userPrompt);
        const response = result.response;
        const text = response.text();
        
        setAiResponse(text);
        
        // Evaluate response quality based on content about Rick and Morty
        const responseEvaluation = await evaluateResponse(text, model);
        setScore(responseEvaluation);
        
        // Update participant score
        const currentParticipant = participants.find(p => p.id === 'current') || participants[0];
        if (currentParticipant) {
          onUpdateScore(currentParticipant.id, responseEvaluation);
        }
        
      } catch (error) {
        console.error('Error calling Gemini API:', error);
        // Fallback response if API fails
        setAiResponse(`**Rick and Morty - Personajes Principales:**

**1. Rick Sanchez**
- Especie: Humano (científico genio)
- Origen: Dimensión C-137
- Personalidad: Genio científico, cínico, alcohólico, egocéntrico
- Estado: Vivo
- Apariciones: Todos los episodios principales

**2. Morty Smith**
- Especie: Humano
- Origen: Tierra (Dimensión C-137)
- Personalidad: Nervioso, inseguro, pero leal y valiente
- Estado: Vivo
- Apariciones: Todos los episodios principales

**3. Jerry Smith**
- Especie: Humano
- Origen: Tierra
- Personalidad: Inseguro, necesitado de aprobación
- Estado: Vivo
- Apariciones: Mayoría de episodios

**4. Beth Smith**
- Especie: Humano
- Origen: Tierra
- Personalidad: Inteligente, conflictuada, similar a Rick
- Estado: Vivo
- Apariciones: Mayoría de episodios

**5. Summer Smith**
- Especie: Humano
- Origen: Tierra
- Personalidad: Adolescente inteligente y adaptable
- Estado: Vivo
- Apariciones: Mayoría de episodios`);
        
        setScore(75);
        const currentParticipant = participants.find(p => p.id === 'current') || participants[0];
        if (currentParticipant) {
          onUpdateScore(currentParticipant.id, 75);
        }
      }
      
      setIsLoading(false);
    }
  };

  const evaluateResponse = async (response: string, model: any): Promise<number> => {
    try {
      const evaluationPrompt = `Evalúa este prompt para obtener información de Rick and Morty en una escala de 0-100:

Prompt a evaluar: "${response}"

Criterios de evaluación:
- Claridad y especificidad del prompt (30 puntos)
- Facilidad de comprensión para una IA (25 puntos)
- Organización y estructura (25 puntos)
- Creatividad y detalle solicitado (20 puntos)

Responde SOLO con un número del 0 al 100.`;

      const result = await model.generateContent(evaluationPrompt);
      const evaluation = result.response;
      const scoreText = evaluation.text().trim();
      const numericScore = parseInt(scoreText.match(/\d+/)?.[0] ?? '70');
      
      return Math.min(Math.max(numericScore, 0), 100);
    } catch (error) {
      console.error('Error evaluating response:', error);
      return 70;
    }
  };

  const getButtonText = () => {
    if (isLoading) return 'Procesando...';
    if (submitted) return 'Enviado';
    return 'Enviar Prompt';
  };

  return (
    <div className="space-y-6">
      {/* Challenge Description */}
      <Card className="bg-game-card border-primary/30">
        <CardHeader>
          <CardTitle className="text-ultra-contrast text-primary font-gaming">
            📚 Desafío: Crear el Mejor Prompt para Rick and Morty
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-game-card-alt rounded-lg p-4 mb-4 border border-accent/30">
            <pre className="text-sm text-ultra-contrast whitespace-pre-wrap font-mono">
              {challengePrompt}
            </pre>
          </div>
          <div className="text-center text-ultra-contrast">
            <p className="text-lg">⏰ Tiempo restante: <span className="font-bold text-accent animate-pulse-glow text-ultra-contrast">{timeLeft}s</span></p>
          </div>
        </CardContent>
      </Card>

      {/* Prompt Input */}
      <Card className="bg-game-card border-secondary/30">
        <CardHeader>
          <CardTitle className="text-ultra-contrast text-secondary font-gaming">Tu Prompt</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Textarea
            placeholder="Escribe aquí tu prompt para obtener información sobre Rick and Morty..."
            value={userPrompt}
            onChange={(e) => setUserPrompt(e.target.value)}
            className="textarea-game min-h-32"
            disabled={submitted}
          />
          <div className="flex justify-between items-center">
            <Badge variant="outline" className="border-accent text-accent bg-accent/10 text-ultra-contrast">
              Caracteres: {userPrompt.length}
            </Badge>
            <Button 
              onClick={handleSubmit}
              disabled={!userPrompt.trim() || submitted || isLoading}
              className="btn-game-success text-ultra-contrast"
            >
              {getButtonText()}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* AI Response */}
      {(submitted || aiResponse) && (
        <Card className="bg-game-card border-purple-500/30">
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="text-ultra-contrast text-purple-400 font-gaming">Respuesta de la IA</CardTitle>
              {score > 0 && (
                <Badge className="bg-purple-500 text-white text-ultra-contrast font-semibold animate-glow">
                  Puntuación: {score}/100
                </Badge>
              )}
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-400"></div>
                <span className="ml-2 text-purple-300 text-ultra-contrast animate-pulse">Generando respuesta...</span>
              </div>
            ) : (
              <div className="bg-game-card-alt rounded-lg p-4 border border-purple-500/20">
                <pre className="text-sm text-ultra-contrast whitespace-pre-wrap">
                  {aiResponse}
                </pre>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      <Card className="bg-game-card border-warning/30">
        <CardContent className="pt-6">
          <div className="text-center text-ultra-contrast">
            <h3 className="font-gaming font-bold mb-2 text-warning animate-glow text-ultra-contrast">💡 Consejos para un buen prompt:</h3>
            <ul className="text-sm space-y-1 text-left max-w-2xl mx-auto text-ultra-contrast">
              <li>• Sé específico sobre qué información necesitas</li>
              <li>• Usa un lenguaje claro y directo</li>
              <li>• Especifica el formato de respuesta que prefieres</li>
              <li>• Incluye contexto relevante sobre Rick and Morty</li>
              <li>• Menciona la API si quieres datos específicos</li>
              <li>• Pide ejemplos o detalles específicos</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PromptBattle;

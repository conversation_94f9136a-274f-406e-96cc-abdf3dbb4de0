import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Participant } from '@/pages/Index';

interface ErrorDetectorProps {
  timeLeft: number;
  participants: Participant[];
  onUpdateScore: (participantId: string, points: number) => void;
}

const buggyCode = `// Script para resumir texto usando la API de Gemini
// Simula errores comunes al confundir APIs (Gemini, OpenAI, Azure)
import { GeminiApiClient } from 'gemini-api-client';

async function summarizeArticleWithGemini(articleText) {
  const client = new GeminiApiClient({
    apiKey: 'sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
  });

  const serviceStatus = await client.checkStatus();
  if (!serviceStatus.isOperational) {
    throw new Error('Servicio Gemini no disponible.');
  }

  const detectedLanguage = await client.detectLanguageAzure(articleText.substring(0, 50));

  let targetModel = 'gemini-pro-summarizer';

  if (detectedLanguage.code !== 'en') {
    targetModel = 'azure-cognitive-translator/english-variant';
  }

  const summary = await client.generateSummary(articleText, {
    model: targetModel,
    engine: 'text-davinci-003',
  });

  return summary.text;
}`;

const errors = [
  {
    line: 7,
    error: "API Key de OpenAI (formato 'sk-...') usada con cliente Gemini.",
    explanation: "El cliente Gemini esperaría una API Key de Google. Usar una clave de OpenAI aquí es una confusión de servicios o una alucinación de la IA.",
    severity: "medio"
  },
  {
    line: 16,
    error: "Método 'detectLanguageAzure' inexistente en cliente Gemini.",
    explanation: "La IA inventó un método con el sufijo 'Azure', intentando usar una funcionalidad de Azure Cognitive Services en un cliente Gemini. Las APIs son distintas.",
    severity: "medio"
  },
  {
    line: 22,
    error: "Intento de usar un identificador de modelo de Azure ('azure-cognitive-translator/...') con Gemini.",
    explanation: "Gemini utiliza sus propios identificadores de modelos. Referenciar un modelo con un nombre claramente de Azure para una operación de Gemini es una alucinación sobre la interoperabilidad de modelos.",
    severity: "crítico"
  },
  {
    line: 27,
    error: "Uso de parámetro 'engine' y valor de OpenAI ('text-davinci-003') en método de Gemini.",
    explanation: "El parámetro 'engine' y modelos como 'text-davinci-003' son específicos de OpenAI. Gemini usaría diferentes parámetros y nombres de modelos.",
    severity: "medio"
  }
];

const ErrorDetector = ({ timeLeft, participants, onUpdateScore }: ErrorDetectorProps) => {
  const [foundErrors, setFoundErrors] = useState<number[]>([]);
  const [submitted, setSubmitted] = useState(false);
  const [showResults, setShowResults] = useState(false);

  const toggleError = (lineNumber: number) => {
    if (!submitted) {
      setFoundErrors(prev => 
        prev.includes(lineNumber)
          ? prev.filter(line => line !== lineNumber)
          : [...prev, lineNumber]
      );
    }
  };

  const handleSubmit = () => {
    setSubmitted(true);
    setTimeout(() => setShowResults(true), 1000);
    
    // Calculate score based on correctly identified errors
    const correctErrors = foundErrors.filter(line => 
      errors.some(error => error.line === line)
    );
    const incorrectErrors = foundErrors.filter(line => 
      !errors.some(error => error.line === line)
    );
    
    const points = Math.max(0, correctErrors.length * 25 - incorrectErrors.length * 5);
    if (participants.length > 0) {
        onUpdateScore(participants[0].id, points);
    } else {
        onUpdateScore('default-player', points);
    }
  };

  const getCodeLines = () => {
    return buggyCode.split('\n').map((line, index) => ({
      number: index + 1,
      content: line,
      hasError: errors.some(error => error.line === index + 1),
      isSelected: foundErrors.includes(index + 1)
    }));
  };

  const getCorrectFinds = () => {
    return foundErrors.filter(line => 
      errors.some(error => error.line === line)
    ).length;
  };



  return (
    <div className="space-y-6">
      <Card className="bg-game-card border-error/30">
        <CardHeader>
          <CardTitle className="text-game-title text-error font-gaming">
            🔍 Cazador de Alucinaciones de IA
          </CardTitle>
          <div className="space-y-2 text-readable">
            <p>
              Una IA generó este código que intenta usar la API de "Gemini" pero comete errores comunes al mezclarla con otras APIs.
            </p>
            <p className="text-sm bg-warning/20 p-3 rounded border border-warning/30 text-high-contrast">
              💡 <strong>Contexto:</strong> Las IAs (o a veces los desarrolladores) pueden confundir parámetros, nombres de métodos o claves de API entre diferentes servicios de IA. 
              Tu misión es encontrar estos errores.
            </p>
          </div>
        </CardHeader>
      </Card>

      {/* Code with line numbers */}
      <Card className="bg-game-card border-accent/30">
        <CardHeader>
          <CardTitle className="text-game-subtitle text-accent font-gaming">
            📝 Código a Analizar
          </CardTitle>
          <p className="text-high-contrast text-sm">
            Haz clic en las líneas donde creas que hay una confusión o alucinación de la IA.
          </p>
        </CardHeader>
        <CardContent>
          <div className="bg-game-card-alt rounded-lg border border-accent/20 overflow-hidden">
            {getCodeLines().map((line) => (
              <div
                key={line.number}
                className={`flex hover:bg-accent/10 cursor-pointer transition-colors ${
                  line.isSelected ? 'bg-error/30 border-l-4 border-error' : ''
                } ${
                  showResults && line.hasError && line.isSelected
                    ? 'bg-success/30 border-l-4 border-success' 
                    : ''
                } ${
                  showResults && line.hasError && !line.isSelected
                    ? 'bg-error/20 border-l-4 border-error'
                    : ''
                } ${
                  showResults && !line.hasError && line.isSelected
                    ? 'bg-warning/20 border-l-4 border-warning'
                    : ''
                }`}
                onClick={() => toggleError(line.number)}
              >
                <div className="w-12 flex-shrink-0 text-muted text-sm text-right pr-4 py-2 bg-background/50 border-r border-accent/30">
                  {line.number}
                </div>
                <div className="flex-1 px-4 py-2">
                  <code className="text-sm text-readable font-mono whitespace-pre">
                    {line.content || ' '}
                  </code>
                </div>
                {line.isSelected && !showResults && (
                  <div className="flex-shrink-0 px-4 py-2 text-error">
                    🔍
                  </div>
                )}
                {showResults && line.hasError && line.isSelected && (
                  <div className="flex-shrink-0 px-4 py-2 text-success">
                    ✅
                  </div>
                )}
                {showResults && line.hasError && !line.isSelected && (
                  <div className="flex-shrink-0 px-4 py-2 text-error">
                    ❌
                  </div>
                )}
                 {showResults && !line.hasError && line.isSelected && (
                  <div className="flex-shrink-0 px-4 py-2 text-orange-500">
                    ⚠️
                  </div>
                )}
              </div>
            ))}
          </div>
          
          <div className="mt-4 flex justify-between items-center">
            <div className="text-sm text-high-contrast">
              Errores sospechosos marcados: {foundErrors.length}
            </div>
            {!submitted && (
              <Button 
                onClick={handleSubmit}
                disabled={foundErrors.length === 0 || timeLeft === 0}
                className="btn-game-success animate-glow"
              >
                🚀 Enviar Análisis
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      {showResults && (
        <div className="space-y-4">
          <Card className="bg-game-card border-success/30">
            <CardContent className="text-center py-6">
              <h3 className="text-game-title text-success font-gaming mb-2 animate-glow">
                📊 Resultados del Análisis
              </h3>
              <p className="text-xl text-readable mb-2">
                {getCorrectFinds()} de {errors.length} errores encontrados correctamente
              </p>
              <Badge className="bg-success text-success-foreground text-lg font-semibold animate-pulse-glow">
                +{Math.max(0, getCorrectFinds() * 25 - (foundErrors.length - getCorrectFinds()) * 5)} puntos
              </Badge>
            </CardContent>
          </Card>

          {/* Error Details */}
          <Card className="bg-game-card border-primary/30">
            <CardHeader>
              <CardTitle className="text-game-subtitle text-primary font-gaming">
                🎭 Errores de API Detectados
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {errors.map((error) => {
                  let severityColor = 'bg-warning';
                  if (error.severity === 'crítico') severityColor = 'bg-error';
                  else if (error.severity === 'alto') severityColor = 'bg-destructive';
                  
                  return (
                    <div 
                      key={`error-${error.line}`}
                      className={`p-4 rounded-lg border ${
                        foundErrors.includes(error.line)
                          ? 'bg-success/20 border-success/30'
                          : 'bg-error/20 border-error/30'
                      }`}
                    >
                      <div className="flex justify-between items-start mb-2">
                        <span className="font-semibold text-accent">
                          Línea {error.line}
                        </span>
                        <div className="flex space-x-2">
                          <Badge className={`${severityColor} text-background font-semibold`}>
                            {error.severity}
                          </Badge>
                          {foundErrors.includes(error.line) && (
                            <Badge className="bg-success text-success-foreground animate-glow">
                              ✅ Encontrado
                            </Badge>
                          )}
                           {!foundErrors.includes(error.line) && (
                            <Badge className="bg-error text-error-foreground">
                              ❌ Omitido
                            </Badge>
                          )}
                        </div>
                      </div>
                      <p className="text-readable font-medium mb-1">{error.error}</p>
                      <p className="text-high-contrast text-sm">{error.explanation}</p>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Tips */}
      <Card className="bg-game-card border-warning/30">
        <CardContent className="pt-6">
          <h3 className="font-gaming font-semibold text-warning mb-2 animate-glow">💡 Cómo identificar estos errores:</h3>
          <ul className="text-sm text-high-contrast space-y-1">
            <li>• <strong>Formatos de API Key incorrectos:</strong> ¿La API Key (ej. <code>sk-...</code>) corresponde al servicio que se dice usar (ej. Gemini)?</li>
            <li>• <strong>Nombres de métodos de otros servicios:</strong> ¿Un método llamado <code>...Azure()</code> o <code>...OpenAI()</code> en un cliente de Gemini?</li>
            <li>• <strong>Parámetros específicos de otras APIs:</strong> ¿Se usa un parámetro como <code>engine: 'davinci-...'</code> (OpenAI) en una función de Gemini?</li>
            <li>• <strong>Referencia a modelos de otros proveedores:</strong> ¿Se intenta usar un nombre de modelo de Azure o AWS con la API de Gemini?</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
};

export default ErrorDetector;

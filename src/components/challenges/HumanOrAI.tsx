
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Participant } from '@/pages/Index';

interface HumanOrAIProps {
  timeLeft: number;
  participants: Participant[];
  onUpdateScore: (participantId: string, points: number) => void;
}

const codeSnippets = [
  {
    id: 1,
    code: `function quickSort(arr) {
  if (arr.length <= 1) return arr;
  
  const pivot = arr[Math.floor(arr.length / 2)];
  const left = arr.filter(x => x < pivot);
  const right = arr.filter(x => x > pivot);
  
  return [...quickSort(left), pivot, ...quickSort(right)];
}`,
    isAI: false,
    explanation: "Estilo humano: uso de nombres descriptivos y estructura clara"
  },
  {
    id: 2,
    code: `const calculateFactorial = (n) => {
  if (n === 0 || n === 1) {
    return 1;
  }
  return n * calculateFactorial(n - 1);
};`,
    isAI: true,
    explanation: "Estilo IA: estructura muy textbook y comentarios típicos"
  },
  {
    id: 3,
    code: `// Messy human code with quirks
let data = [];
for(let i=0;i<items.length;i++){
    if(items[i].status=='active'){
        data.push(items[i]);
    }
}
return data;`,
    isAI: false,
    explanation: "Claramente humano: inconsistencias en spacing y estilo"
  },
  {
    id: 4,
    code: `/**
 * This function validates email addresses using regular expressions
 * @param {string} email - The email address to validate
 * @returns {boolean} - True if valid, false otherwise
 */
function validateEmail(email) {
  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
  return emailRegex.test(email);
}`,
    isAI: true,
    explanation: "Típico IA: documentación perfecta y estructura muy formal"
  },
  {
    id: 5,
    code: `const handleClick = (e) => {
  e.preventDefault();
  // TODO: fix this later
  console.log('clicked');
  doSomething();
};`,
    isAI: false,
    explanation: "Humano: comentarios informales y console.log para debug"
  }
];

const HumanOrAI = ({ timeLeft, participants, onUpdateScore }: HumanOrAIProps) => {
  const [answers, setAnswers] = useState<{ [key: number]: boolean | null }>({});
  const [submitted, setSubmitted] = useState(false);
  const [showResults, setShowResults] = useState(false);

  const handleAnswer = (snippetId: number, isAI: boolean) => {
    if (!submitted) {
      setAnswers(prev => ({ ...prev, [snippetId]: isAI }));
    }
  };

  const handleSubmit = () => {
    setSubmitted(true);
    setTimeout(() => setShowResults(true), 1000);
    
    // Calculate score
    let correctAnswers = 0;
    codeSnippets.forEach(snippet => {
      if (answers[snippet.id] === snippet.isAI) {
        correctAnswers++;
      }
    });
    
    const points = correctAnswers * 20;
    onUpdateScore(participants[0]?.id || '', points);
  };

  const getCorrectAnswers = () => {
    return codeSnippets.filter(snippet => answers[snippet.id] === snippet.isAI).length;
  };

  return (
    <div className="space-y-6">
      <Card className="bg-game-card border-primary/30">
        <CardHeader>
          <CardTitle className="text-ultra-contrast text-primary font-gaming">
            🤖 ¿Humano o IA?
          </CardTitle>
          <p className="text-ultra-contrast">
            Examina cada fragmento de código y decide si fue escrito por un humano o generado por IA
          </p>
        </CardHeader>
      </Card>

      <div className="grid gap-4">
        {codeSnippets.map((snippet, index) => (
          <Card key={snippet.id} className="bg-game-card border-secondary/30">
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle className="text-ultra-contrast text-secondary font-gaming">
                  Fragmento #{index + 1}
                </CardTitle>
                {showResults && (
                  <Badge className={answers[snippet.id] === snippet.isAI ? 'bg-success text-success-foreground animate-glow text-ultra-contrast' : 'bg-error text-error-foreground text-ultra-contrast'}>
                    {answers[snippet.id] === snippet.isAI ? '✅ Correcto' : '❌ Incorrecto'}
                  </Badge>
                )}
              </div>
            </CardHeader>
            <CardContent>
              <div className="bg-game-card-alt p-4 rounded-lg border border-accent/20 mb-4">
                <pre className="text-sm text-ultra-contrast whitespace-pre-wrap font-mono overflow-x-auto">
                  {snippet.code}
                </pre>
              </div>
              
              <div className="flex space-x-4">
                <Button
                  variant={answers[snippet.id] === false ? "default" : "outline"}
                  onClick={() => handleAnswer(snippet.id, false)}
                  disabled={submitted}
                  className={`flex-1 text-ultra-contrast ${
                    answers[snippet.id] === false 
                      ? 'btn-game-primary' 
                      : 'border-primary text-primary hover:bg-primary/20'
                  }`}
                >
                  👨‍💻 Humano
                </Button>
                <Button
                  variant={answers[snippet.id] === true ? "default" : "outline"}
                  onClick={() => handleAnswer(snippet.id, true)}
                  disabled={submitted}
                  className={`flex-1 text-ultra-contrast ${
                    answers[snippet.id] === true 
                      ? 'btn-game-secondary' 
                      : 'border-secondary text-secondary hover:bg-secondary/20'
                  }`}
                >
                  🤖 IA
                </Button>
              </div>

              {showResults && (
                <div className="mt-4 p-3 bg-game-card-alt rounded-lg border border-accent/30">
                  <p className="text-sm text-ultra-contrast">
                    <span className="font-semibold text-accent text-ultra-contrast">Respuesta correcta:</span> {snippet.isAI ? '🤖 IA' : '👨‍💻 Humano'}
                  </p>
                  <p className="text-xs text-ultra-contrast mt-1">{snippet.explanation}</p>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {!submitted && Object.keys(answers).length === codeSnippets.length && (
        <div className="text-center">
          <Button 
            onClick={handleSubmit}
            className="btn-game-success h-12 px-8 text-lg font-bold animate-glow text-ultra-contrast"
          >
            🚀 Enviar Respuestas
          </Button>
        </div>
      )}

      {showResults && (
        <Card className="bg-game-card border-success/30">
          <CardContent className="text-center py-6">
            <h3 className="text-ultra-contrast text-success font-gaming mb-2 animate-glow">
              📊 Resultados
            </h3>
            <p className="text-xl text-ultra-contrast mb-2">
              {getCorrectAnswers()} de {codeSnippets.length} correctas
            </p>
            <Badge className="bg-success text-success-foreground text-lg font-semibold animate-pulse-glow text-ultra-contrast">
              +{getCorrectAnswers() * 20} puntos
            </Badge>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default HumanOrAI;

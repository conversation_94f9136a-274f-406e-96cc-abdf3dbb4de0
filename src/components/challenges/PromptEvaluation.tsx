import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Participant } from '@/pages/Index';
import { GoogleGenerativeAI } from '@google/generative-ai';

interface PromptEvaluationProps {
  timeLeft: number;
  participants: Participant[];
  onUpdateScore: (participantId: string, points: number) => void;
}

const PromptEvaluation = ({ timeLeft, participants, onUpdateScore }: PromptEvaluationProps) => {
  const [userPrompt, setUserPrompt] = useState('');
  const [submitted, setSubmitted] = useState(false);
  const [evaluation, setEvaluation] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [score, setScore] = useState(0);

  const challengePrompt = `Crea un prompt para que una IA genere una aplicación de consola en **C#** que consulte la cotización del Dólar Blue en Argentina, obteniendo los datos de una API pública como \`bluelytics.com.ar\`. Tu prompt debe indicar que la aplicación, tras obtener los valores de compra, venta y la fecha de actualización, debe devolver toda esa información junta en un único formato **JSON**. El desafío es que tu prompt sea tan preciso que la IA infiera por sí misma una estructura JSON lógica y coherente para la salida, sin que tú se la definas.`;

  const evaluationTemplate = `### Instrucciones para el Evaluador

Actúa como un **Arquitecto de Software** experimentado. Tu tarea es evaluar la calidad y efectividad de un prompt escrito por un participante, cuyo objetivo era generar una aplicación de consola en C# según un requerimiento específico. Debes ser riguroso, justo y valorar tanto la claridad técnica como el cumplimiento de los requisitos.

### Criterios de Evaluación (Total: 100 puntos)

* **A. Especificación Técnica (40 puntos):**
    * **Claridad de Herramientas (20 pts):** ¿El prompt especifica claramente que el lenguaje es **C#** y el tipo de aplicación es de **consola**? ¿Sugiere o guía a la IA sobre las librerías a utilizar (ej. \`HttpClient\`, \`System.Text.Json\`)?
    * **Precisión de la Fuente de Datos (20 pts):** ¿El prompt menciona correctamente la API o instruye de forma inequívoca a buscar una API pública para tal fin?

* **B. Cumplimiento de Requisitos Funcionales (50 puntos):**
    * **Extracción de Datos (20 pts):** ¿El prompt solicita explícitamente que se obtengan los tres campos requeridos: **valor de compra, valor de venta y fecha de actualización**?
    * **Formato de Salida (30 pts):** ¿Instruye CLARAMENTE que la única salida del programa debe ser un formato **JSON**? ¿Qué tan bien manejó la ambigüedad de la estructura, guiando a la IA para que infiera una estructura lógica?

* **C. Calidad General del Prompt (10 puntos):**
    * **Concisión y Eficacia (10 pts):** ¿El prompt es directo, sin ambigüedades y es probable que genere el resultado correcto en el primer intento?

Evalúa este prompt del participante:

"${userPrompt}"

Responde ÚNICAMENTE con un número del 0 al 100 que represente la puntuación total, seguido de un salto de línea y luego una breve explicación de máximo 200 palabras sobre los puntos fuertes y áreas de mejora.`;

  const handleSubmit = async () => {
    if (userPrompt.trim() && !submitted) {
      setSubmitted(true);
      setIsLoading(true);
      
      try {
        // Initialize Gemini API
        const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
        if (!apiKey) {
          throw new Error('API key not found');
        }
        
        const genAI = new GoogleGenerativeAI(apiKey);
        const model = genAI.getGenerativeModel({ model: "gemini-pro" });

        const result = await model.generateContent(evaluationTemplate);
        const response = result.response;
        const text = response.text();
        
        setEvaluation(text);
        
        // Extract score from response (first line should be the score)
        const lines = text.split('\n');
        const scoreMatch = lines[0].match(/\d+/);
        const extractedScore = scoreMatch ? parseInt(scoreMatch[0]) : 0;
        
        setScore(extractedScore);
        onUpdateScore(participants[0]?.id || '', extractedScore);
      } catch (error) {
        console.error('Error evaluating prompt:', error);
        setEvaluation('Error: No se pudo evaluar el prompt. Asegúrate de tener configurada la API key de Gemini.');
      }
      
      setIsLoading(false);
    }
  };

  const getButtonText = () => {
    if (isLoading) return '🤖 Evaluando tu prompt...';
    if (submitted) return '✅ Prompt Evaluado';
    return '🚀 Evaluar mi Prompt';
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-success';
    if (score >= 60) return 'text-warning';
    return 'text-error';
  };

  const getScoreBadge = (score: number) => {
    if (score >= 90) return { class: 'bg-success text-success-foreground', text: '🏆 Excelente' };
    if (score >= 80) return { class: 'bg-success text-success-foreground', text: '⭐ Muy Bueno' };
    if (score >= 70) return { class: 'bg-warning text-warning-foreground', text: '👍 Bueno' };
    if (score >= 60) return { class: 'bg-warning text-warning-foreground', text: '👌 Regular' };
    return { class: 'bg-error text-error-foreground', text: '📝 Necesita Mejoras' };
  };

  return (
    <div className="space-y-6">
      <Card className="bg-game-card border-primary/30">
        <CardHeader>
          <CardTitle className="text-ultra-contrast text-primary font-gaming">
            💻 Evaluación de Prompt Técnico
          </CardTitle>
          <p className="text-ultra-contrast">
            Crea un prompt para generar una aplicación de C# que consulte el Dólar Blue argentino
          </p>
        </CardHeader>
      </Card>

      <Card className="bg-game-card border-secondary/30">
        <CardHeader>
          <CardTitle className="text-ultra-contrast text-secondary font-gaming">
            📋 La Consigna
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-game-card-alt p-4 rounded-lg border border-accent/20">
            <p className="text-ultra-contrast text-sm leading-relaxed whitespace-pre-line">
              {challengePrompt}
            </p>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-game-card border-accent/30">
        <CardHeader>
          <CardTitle className="text-ultra-contrast text-accent font-gaming">
            ✍️ Tu Prompt
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Textarea
            placeholder="Escribe aquí tu prompt para generar la aplicación de C# que consulte el Dólar Blue..."
            value={userPrompt}
            onChange={(e) => setUserPrompt(e.target.value)}
            className="min-h-[200px] bg-game-card-alt border-accent/30 text-ultra-contrast placeholder:text-muted-foreground/70 resize-none"
            disabled={submitted}
          />
          <div className="flex justify-between items-center mt-4">
            <span className="text-sm text-ultra-contrast">
              {userPrompt.length} caracteres
            </span>
            <Button
              onClick={handleSubmit}
              disabled={!userPrompt.trim() || submitted || isLoading}
              className="btn-game-success font-bold text-ultra-contrast"
            >
              {getButtonText()}
            </Button>
          </div>
        </CardContent>
      </Card>

      {(submitted || evaluation) && (
        <Card className="bg-game-card border-success/30">
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="text-ultra-contrast text-success font-gaming">
                🎯 Evaluación del Arquitecto
              </CardTitle>
              {score > 0 && (
                <div className="flex items-center space-x-2">
                  <Badge className={`${getScoreBadge(score).class} animate-glow text-ultra-contrast`}>
                    {getScoreBadge(score).text}
                  </Badge>
                  <span className={`text-2xl font-bold ${getScoreColor(score)} animate-pulse`}>
                    {score}/100
                  </span>
                </div>
              )}
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                <span className="ml-3 text-ultra-contrast">El arquitecto está evaluando tu prompt...</span>
              </div>
            ) : evaluation ? (
              <div className="space-y-4">
                <div className="bg-game-card-alt p-4 rounded-lg border border-accent/20">
                  <pre className="text-sm text-ultra-contrast whitespace-pre-wrap font-mono">
                    {evaluation}
                  </pre>
                </div>
                {score > 0 && (
                  <div className="text-center">
                    <Badge className="bg-success text-success-foreground text-lg font-semibold animate-pulse-glow text-ultra-contrast">
                      +{score} puntos
                    </Badge>
                  </div>
                )}
              </div>
            ) : null}
          </CardContent>
        </Card>
      )}

      {/* Tips */}
      <Card className="bg-game-card border-warning/30">
        <CardContent className="pt-6">
          <h3 className="font-gaming font-semibold text-warning mb-2 animate-glow text-ultra-contrast">💡 Tips para prompts técnicos:</h3>
          <ul className="text-sm text-ultra-contrast space-y-1">
            <li>• <strong>Especifica el lenguaje:</strong> Menciona claramente "C#" y "aplicación de consola"</li>
            <li>• <strong>Define la fuente de datos:</strong> API específica o instrucciones para encontrarla</li>
            <li>• <strong>Campos requeridos:</strong> Compra, venta y fecha de actualización</li>
            <li>• <strong>Formato de salida:</strong> JSON como única salida del programa</li>
            <li>• <strong>Librerías sugeridas:</strong> HttpClient, System.Text.Json, etc.</li>
            <li>• <strong>Estructura del JSON:</strong> Deja que la IA infiera una estructura lógica</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
};

export default PromptEvaluation;

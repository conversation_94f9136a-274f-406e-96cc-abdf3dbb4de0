
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Participant } from '@/pages/Index';

interface WaitingRoomProps {
  participants: Participant[];
  currentUser: Participant | null;
  onStartGame: () => void;
  roomId?: string;
}

const WaitingRoom = ({ participants, currentUser, onStartGame, roomId }: WaitingRoomProps) => {
  const isHost = currentUser?.isHost;

  return (
    <div className="max-w-4xl mx-auto">
      <Card className="bg-game-card border-2 border-cyan-500/40 backdrop-blur-sm shadow-2xl">
        <CardHeader className="text-center">
          <CardTitle className="text-4xl font-arcade text-neon-accent mb-4 animate-neon-pulse">
            ⏳ SALA DE ESPERA
          </CardTitle>
          <p className="text-high-contrast font-retro text-xl">
            Esperando que todos los guerreros se unan a la batalla épica...
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-6">
            {/* Participants List */}
            <div>
              <h3 className="text-2xl font-arcade text-neon-primary mb-6 animate-text-glow">
                👥 PARTICIPANTES ({participants.length})
              </h3>
              <div className="space-y-3 max-h-60 overflow-y-auto">
                {participants.map((participant, index) => (
                  <div 
                    key={participant.id}
                    className="participant-card flex items-center justify-between p-4 rounded-lg"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold shadow-lg text-xl">
                        {index + 1}
                      </div>
                      <span className="font-arcade text-high-contrast text-xl">{participant.name}</span>
                    </div>
                    <div className="flex space-x-3">
                      {participant.isHost && (
                        <Badge className="bg-yellow-500 text-black font-arcade shadow-lg text-lg px-3 py-1">👑 HOST</Badge>
                      )}
                      {participant.id === currentUser?.id && (
                        <Badge className="bg-green-500 text-white font-arcade shadow-lg text-lg px-3 py-1">TÚ</Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Game Info */}
            <div>
              <h3 className="text-2xl font-arcade text-neon-primary mb-6 animate-text-glow">
                🎮 INFORMACIÓN DEL JUEGO
              </h3>
              <div className="space-y-6">
                {roomId && (
                  <div className="p-4 bg-yellow-500/10 rounded-lg border-2 border-yellow-500/40">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <span className="text-3xl">🔑</span>
                        <div>
                          <h4 className="font-arcade text-high-contrast text-lg">Código de Sala</h4>
                          <p className="text-yellow-300 font-mono text-2xl font-bold tracking-wider">{roomId}</p>
                        </div>
                      </div>
                      <Button
                        onClick={() => navigator.clipboard.writeText(roomId)}
                        className="btn-game-secondary text-sm px-3 py-1"
                      >
                        📋 Copiar
                      </Button>
                    </div>
                  </div>
                )}

                <div className="p-6 bg-game-card-alt rounded-lg border-3 border-cyan-500/40">
                  <h4 className="font-arcade text-neon-accent mb-4 text-xl">📋 FASES DEL BATTLE:</h4>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-readable text-lg">🎯 Ronda 1: Maestría de Prompts</span>
                      <span className="timer-text">3 MIN</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-readable text-lg">🤖 Ronda 2: ¿Humano o IA?</span>
                      <span className="timer-text">3 MIN</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-readable text-lg">🔍 Ronda 3: Cazador de Errores</span>
                      <span className="timer-text">3 MIN</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-readable text-lg">🏅 Ronda 4: Elección Perfecta</span>
                      <span className="timer-text">3 MIN</span>
                    </div>
                  </div>
                </div>

                <div className="p-6 bg-gradient-to-r from-purple-800/30 to-pink-800/30 rounded-lg border-3 border-purple-500/50">
                  <h4 className="font-arcade text-neon-success mb-4 text-xl">🏆 SISTEMA DE PUNTOS:</h4>
                  <p className="text-readable text-lg">
                    🚀 Cada ronda otorga puntos basados en <span className="text-neon-success">PRECISIÓN</span> y <span className="text-neon-primary">VELOCIDAD</span>.<br/>
                    ⚡ ¡El guerrero con más puntos conquista la batalla épica!
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Start Game Button */}
          {isHost && (
            <div className="mt-8 text-center">
              <Button 
                onClick={onStartGame}
                className="h-14 px-8 text-xl btn-game-success hover:scale-105 transition-all duration-300 shadow-xl text-ultra-contrast"
                disabled={participants.length < 1}
              >
                🚀 INICIAR BATALLA ÉPICA
              </Button>
              {participants.length < 2 && (
                <p className="text-warning mt-3 text-base font-bold animate-pulse-glow text-ultra-contrast">
                  💡 Consejo: La batalla es más épica con más participantes
                </p>
              )}
            </div>
          )}

          {!isHost && (
            <div className="mt-8 text-center p-4 bg-game-card-alt rounded-lg border-2 border-yellow-500/40">
              <p className="text-gray-200 font-semibold text-lg text-ultra-contrast">
                ⏳ Esperando que <span className="text-warning font-bold glow-text text-ultra-contrast">
                  {participants.find(p => p.isHost)?.name}
                </span> inicie la batalla épica...
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default WaitingRoom;

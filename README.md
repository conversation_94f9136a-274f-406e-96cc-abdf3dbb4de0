# Prompt Arena Challenge

Un juego interactivo multijugador donde guerreros de todo el mundo compiten en una arena global en desafíos relacionados con prompts de IA, detección de contenido generado por IA, y evaluación de respuestas.

## 🌍 Arena Global

**¡Todos los jugadores luchan en la misma batalla!** No hay salas separadas - cuando te unes al juego, automáticamente entras a la arena global donde puedes competir con jugadores de todo el mundo en tiempo real.

## Características

- **🌍 Arena Global Única**: Todos los jugadores se unen automáticamente a la misma batalla
- **⚔️ Multijugador en Tiempo Real**: Compite con otros guerreros usando Socket.IO
- **4 Tipos de Desafíos**:
  - 🎯 Maestría de Prompts: Crea prompts efectivos
  - 🤖 ¿Humano o IA?: Identifica contenido generado por IA
  - 🔍 Cazador de Errores: Detecta alucinaciones de IA
  - 🏅 Elección Perfecta: Selecciona los mejores prompts
- **Sistema de Puntuación**: Basado en precisión y velocidad
- **Interfaz Gaming**: Diseño épico con animaciones y efectos

## Tecnologías

- **Frontend**: React + TypeScript + Vite
- **Backend**: Node.js + Express + Socket.IO
- **UI**: Tailwind CSS + shadcn/ui
- **Tiempo Real**: Socket.IO para multijugador
- **Despliegue**: Vercel

## Instalación y Desarrollo

```bash
# Instalar dependencias
npm install

# Ejecutar servidor y frontend juntos
npm run dev:full

# O ejecutar por separado:
# Servidor Socket.IO
npm run server

# Frontend (en otra terminal)
npm run dev

# Construir para producción
npm run build

# Desplegar a Vercel
npm run deploy:vercel
```

## 🎮 Cómo Jugar

1. **🌍 Únete a la Arena Global**: Ingresa tu nombre para entrar automáticamente a la batalla mundial
2. **⏳ Arena Global**: Ve a otros guerreros preparándose para la batalla
3. **⚔️ Batalla Épica**: Compite en 4 rondas de desafíos intensos
4. **🏆 Resultados Globales**: Ve quién conquistó la arena mundial

## 🚀 Características del Multijugador

- **Conexión Automática**: Al abrir la app, te conectas automáticamente al servidor
- **Sala Global**: Todos los jugadores van a la misma arena (ID: `BATALLA_GLOBAL`)
- **Sincronización en Tiempo Real**:
  - Ver jugadores uniéndose y saliendo
  - Puntuaciones actualizadas en vivo
  - Inicio de juego sincronizado
- **Host Automático**: El primer jugador se convierte en host y puede iniciar la batalla

## 📁 Estructura del Proyecto

```
├── server/
│   └── index.js         # Servidor Socket.IO
├── src/
│   ├── components/      # Componentes React
│   │   ├── challenges/  # Componentes de desafíos
│   │   ├── ui/         # Componentes de UI base
│   │   └── ...
│   ├── pages/          # Páginas principales
│   ├── services/       # Servicios (Socket.IO, etc.)
│   └── lib/           # Utilidades
└── package.json        # Scripts y dependencias
```

## 🔧 Scripts Disponibles

- `npm run dev:full` - Ejecuta servidor y frontend juntos
- `npm run server` - Solo el servidor Socket.IO
- `npm run dev` - Solo el frontend
- `npm run build` - Construir para producción
- `npm run deploy:vercel` - Desplegar a Vercel

## 🌐 URLs

- **Frontend Local**: http://localhost:8082 (o puerto disponible)
- **Servidor Socket.IO**: http://localhost:3001
- **Producción**: [Tu URL de Vercel]

## Contribuir

1. Fork el proyecto
2. Crea una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abre un Pull Request

## Licencia

Este proyecto está bajo la Licencia MIT - ver el archivo [LICENSE](LICENSE) para detalles.